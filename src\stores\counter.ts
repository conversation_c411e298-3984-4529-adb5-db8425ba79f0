import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { apiService, type LiveEvent, type Channel } from '@/services/api'

export const useLiveTvStore = defineStore('livetv', () => {
  const events = ref<{ [date: string]: LiveEvent[] }>({})
  const channels = ref<Channel[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const selectedEvent = ref<LiveEvent | null>(null)
  const selectedChannel = ref<string | null>(null)

  const todayEvents = computed(() => {
    const today = new Date().toISOString().split('T')[0]
    return events.value[today] || []
  })

  const liveEvents = computed(() => {
    return apiService.filterLiveEvents(events.value)
  })

  const upcomingEvents = computed(() => {
    return apiService.getUpcomingEvents(events.value, 5)
  })

  const allSports = computed(() => {
    return apiService.getAllSports(events.value)
  })

  const liveChannels = computed(() => {
    return channels.value.filter(channel => channel.isLive)
  })

  const totalEvents = computed(() => {
    return Object.values(events.value).flat().length
  })

  const eventsByDate = computed(() => {
    // Sort dates and return organized events
    const sortedDates = Object.keys(events.value).sort()
    const organized: { [date: string]: LiveEvent[] } = {}

    sortedDates.forEach(date => {
      organized[date] = events.value[date].sort((a, b) => a.unix_timestamp - b.unix_timestamp)
    })

    return organized
  })

  async function fetchEvents(date?: string) {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.getLiveEvents(date)
      events.value = response.events
    } catch (err) {
      error.value = 'Failed to fetch live events'
      console.error('Error fetching events:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchChannels() {
    loading.value = true
    error.value = null

    try {
      const response = await apiService.getChannels()
      channels.value = response
    } catch (err) {
      error.value = 'Failed to fetch channels'
      console.error('Error fetching channels:', err)
    } finally {
      loading.value = false
    }
  }

  function selectEvent(event: LiveEvent) {
    selectedEvent.value = event
    if (event.channels.length > 0) {
      selectedChannel.value = event.channels[0]
    }
  }

  function selectChannel(channelUrl: string) {
    selectedChannel.value = channelUrl
  }

  function clearSelection() {
    selectedEvent.value = null
    selectedChannel.value = null
  }

  function getEventsBySport(sport: string) {
    const filtered: { [date: string]: LiveEvent[] } = {}

    Object.entries(events.value).forEach(([date, dayEvents]) => {
      const sportEvents = dayEvents.filter(event => event.sport === sport)
      if (sportEvents.length > 0) {
        filtered[date] = sportEvents
      }
    })

    return filtered
  }

  function getEventsForDate(date: string) {
    return apiService.getEventsByDate(events.value, date)
  }

  function refreshData() {
    return Promise.all([fetchEvents(), fetchChannels()])
  }

  return {
    // State
    events,
    channels,
    loading,
    error,
    selectedEvent,
    selectedChannel,

    // Computed
    todayEvents,
    liveEvents,
    upcomingEvents,
    allSports,
    liveChannels,
    totalEvents,
    eventsByDate,

    // Actions
    fetchEvents,
    fetchChannels,
    selectEvent,
    selectChannel,
    clearSelection,
    getEventsBySport,
    getEventsForDate,
    refreshData
  }
})
