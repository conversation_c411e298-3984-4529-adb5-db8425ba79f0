import{u as e}from"./index-B7BpRN8I.js";import{S as s,T as t,U as l,R as a,g as n,h as i,d as r,D as c,e as o,i as d,j as g}from"./utils-mFY9R7nY.js";import{d as u,r as m,j as x,m as v,c as b,a as h,b as f,i as p,u as y,t as w,n as C,F as j,A as k,o as E}from"./vendor-zrHeai00.js";const A={class:"min-h-screen bg-gray-50"},S={class:"bg-white shadow-sm"},T={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},L={class:"flex items-center justify-between"},M={class:"text-2xl font-bold text-gray-900 flex items-center"},U={class:"flex items-center space-x-4"},V={class:"text-sm text-gray-500"},N={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},R={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},D={class:"bg-white rounded-lg shadow-sm p-6"},P={class:"flex items-center"},_={class:"p-2 bg-blue-100 rounded-lg"},F={class:"ml-4"},O={class:"text-2xl font-bold text-gray-900"},q={class:"bg-white rounded-lg shadow-sm p-6"},z={class:"flex items-center"},B={class:"p-2 bg-green-100 rounded-lg"},G={class:"ml-4"},H={class:"text-2xl font-bold text-gray-900"},I={class:"bg-white rounded-lg shadow-sm p-6"},J={class:"flex items-center"},K={class:"p-2 bg-red-100 rounded-lg"},Q={class:"ml-4"},W={class:"text-2xl font-bold text-gray-900"},X={class:"bg-white rounded-lg shadow-sm p-6"},Y={class:"flex items-center"},Z={class:"p-2 bg-yellow-100 rounded-lg"},$={class:"ml-4"},ee={class:"text-2xl font-bold text-gray-900"},se={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},te={class:"bg-white rounded-lg shadow-sm"},le={class:"p-6"},ae={class:"space-y-4"},ne={class:"bg-white rounded-lg shadow-sm"},ie={class:"p-6"},re={class:"space-y-4"},ce={class:"bg-white rounded-lg shadow-sm"},oe={class:"p-6"},de={class:"space-y-4"},ge={class:"flex items-center justify-between"},ue={class:"flex items-center justify-between"},me={class:"bg-white rounded-lg shadow-sm"},xe={class:"p-6"},ve={class:"space-y-3"},be={class:"flex-1"},he={class:"text-sm text-gray-900"},fe={class:"text-xs text-gray-500"},pe=u({__name:"AdminView",setup(u){const pe=e(),ye=m(!1),we=m(!1),Ce=m(!0),je=m(!1),ke=m({totalEvents:0,activeUsers:1247,liveChannels:0,totalViews:45678}),Ee=m([{id:1,message:"New event added: Lakers vs Celtics",time:"2 minutes ago"},{id:2,message:"Channel ESPN updated",time:"5 minutes ago"},{id:3,message:"System cache cleared",time:"10 minutes ago"},{id:4,message:"User admin logged in",time:"15 minutes ago"}]),Ae=x(()=>(new Date).toLocaleTimeString()),Se=async()=>{await pe.fetchEvents(),Re()},Te=()=>{alert("Events exported successfully!")},Le=()=>{alert("Testing all channels...")},Me=()=>{alert("Channel management coming soon!")},Ue=()=>{Ce.value=!Ce.value},Ve=()=>{je.value=!je.value},Ne=()=>{alert("Cache cleared successfully!")},Re=()=>{ke.value.totalEvents=Object.values(pe.events).flat().length,ke.value.liveChannels=pe.channels.filter(e=>e.isLive).length};return v(async()=>{await pe.fetchEvents(),await pe.fetchChannels(),Re()}),(e,u)=>(E(),b("div",A,[h("div",S,[h("div",T,[h("div",L,[h("div",null,[h("h1",M,[f(y(s),{class:"w-6 h-6 mr-3 text-blue-600"}),u[2]||(u[2]=p(" Admin Panel "))]),u[3]||(u[3]=h("p",{class:"text-gray-600 mt-1"},"Manage your live TV platform",-1))]),h("div",U,[h("div",V," Last updated: "+w(Ae.value),1)])])])]),h("div",N,[h("div",R,[h("div",D,[h("div",P,[h("div",_,[f(y(t),{class:"w-6 h-6 text-blue-600"})]),h("div",F,[u[4]||(u[4]=h("p",{class:"text-sm font-medium text-gray-600"},"Total Events",-1)),h("p",O,w(ke.value.totalEvents),1)])])]),h("div",q,[h("div",z,[h("div",B,[f(y(l),{class:"w-6 h-6 text-green-600"})]),h("div",G,[u[5]||(u[5]=h("p",{class:"text-sm font-medium text-gray-600"},"Active Users",-1)),h("p",H,w(ke.value.activeUsers),1)])])]),h("div",I,[h("div",J,[h("div",K,[f(y(a),{class:"w-6 h-6 text-red-600"})]),h("div",Q,[u[6]||(u[6]=h("p",{class:"text-sm font-medium text-gray-600"},"Live Channels",-1)),h("p",W,w(ke.value.liveChannels),1)])])]),h("div",X,[h("div",Y,[h("div",Z,[f(y(n),{class:"w-6 h-6 text-yellow-600"})]),h("div",$,[u[7]||(u[7]=h("p",{class:"text-sm font-medium text-gray-600"},"Total Views",-1)),h("p",ee,w(ke.value.totalViews.toLocaleString()),1)])])])]),h("div",se,[h("div",te,[u[11]||(u[11]=h("div",{class:"p-6 border-b border-gray-200"},[h("h2",{class:"text-lg font-semibold text-gray-900"},"Event Management")],-1)),h("div",le,[h("div",ae,[h("button",{onClick:u[0]||(u[0]=e=>ye.value=!0),class:"w-full flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},[f(y(i),{class:"w-5 h-5 mr-2"}),u[8]||(u[8]=p(" Add New Event "))]),h("button",{onClick:Se,class:"w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},[f(y(r),{class:"w-5 h-5 mr-2"}),u[9]||(u[9]=p(" Refresh Events "))]),h("button",{onClick:Te,class:"w-full flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"},[f(y(c),{class:"w-5 h-5 mr-2"}),u[10]||(u[10]=p(" Export Events "))])])])]),h("div",ne,[u[15]||(u[15]=h("div",{class:"p-6 border-b border-gray-200"},[h("h2",{class:"text-lg font-semibold text-gray-900"},"Channel Management")],-1)),h("div",ie,[h("div",re,[h("button",{onClick:u[1]||(u[1]=e=>we.value=!0),class:"w-full flex items-center justify-center px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"},[f(y(i),{class:"w-5 h-5 mr-2"}),u[12]||(u[12]=p(" Add New Channel "))]),h("button",{onClick:Le,class:"w-full flex items-center justify-center px-4 py-3 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors"},[f(y(o),{class:"w-5 h-5 mr-2"}),u[13]||(u[13]=p(" Test All Channels "))]),h("button",{onClick:Me,class:"w-full flex items-center justify-center px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"},[f(y(d),{class:"w-5 h-5 mr-2"}),u[14]||(u[14]=p(" Manage Channels "))])])])]),h("div",ce,[u[19]||(u[19]=h("div",{class:"p-6 border-b border-gray-200"},[h("h2",{class:"text-lg font-semibold text-gray-900"},"System Settings")],-1)),h("div",oe,[h("div",de,[h("div",ge,[u[16]||(u[16]=h("span",{class:"text-sm font-medium text-gray-700"},"Auto-refresh events",-1)),h("button",{onClick:Ue,class:C(["relative inline-flex h-6 w-11 items-center rounded-full transition-colors",Ce.value?"bg-blue-600":"bg-gray-200"])},[h("span",{class:C(["inline-block h-4 w-4 transform rounded-full bg-white transition-transform",Ce.value?"translate-x-6":"translate-x-1"])},null,2)],2)]),h("div",ue,[u[17]||(u[17]=h("span",{class:"text-sm font-medium text-gray-700"},"Maintenance mode",-1)),h("button",{onClick:Ve,class:C(["relative inline-flex h-6 w-11 items-center rounded-full transition-colors",je.value?"bg-red-600":"bg-gray-200"])},[h("span",{class:C(["inline-block h-4 w-4 transform rounded-full bg-white transition-transform",je.value?"translate-x-6":"translate-x-1"])},null,2)],2)]),h("button",{onClick:Ne,class:"w-full flex items-center justify-center px-4 py-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"},[f(y(g),{class:"w-5 h-5 mr-2"}),u[18]||(u[18]=p(" Clear Cache "))])])])]),h("div",me,[u[21]||(u[21]=h("div",{class:"p-6 border-b border-gray-200"},[h("h2",{class:"text-lg font-semibold text-gray-900"},"Recent Activity")],-1)),h("div",xe,[h("div",ve,[(E(!0),b(j,null,k(Ee.value,e=>(E(),b("div",{key:e.id,class:"flex items-center space-x-3"},[u[20]||(u[20]=h("div",{class:"w-2 h-2 bg-blue-500 rounded-full"},null,-1)),h("div",be,[h("p",he,w(e.message),1),h("p",fe,w(e.time),1)])]))),128))])])])])])]))}});export{pe as default};
