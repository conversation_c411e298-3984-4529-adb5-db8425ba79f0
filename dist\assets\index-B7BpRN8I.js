const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/LiveView-CBLP0BYv.js","assets/vendor-zrHeai00.js","assets/utils-mFY9R7nY.js","assets/LiveView-CKs-bP8s.css","assets/ScheduleView-DZ9ZeAtX.js","assets/AdminView-C0yG8DH6.js","assets/AboutView-BcoS1ILR.js","assets/AboutView-CSIvawM9.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,s,a)=>((t,s,a)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[s]=a)(t,"symbol"!=typeof s?s+"":s,a);import{d as s,r as a,c as l,a as n,w as r,b as o,e as i,f as c,n as u,g as d,u as m,v as h,o as p,i as v,j as g,k as f,t as x,R as b,l as w,m as y,p as _,q as S,s as E,x as k}from"./vendor-zrHeai00.js";import{M as A,X as L,T as C,H as P,R as j,C as D,S as U,F as T,a as O,I as V,Y as M,b as F,U as B,P as N}from"./utils-mFY9R7nY.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const I={class:"bg-white shadow-lg sticky top-0 z-50"},R={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},$={class:"flex justify-between items-center h-16"},H={class:"flex items-center"},W={class:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"},q={class:"hidden md:block"},G={class:"ml-10 flex items-baseline space-x-4"},Y={class:"md:hidden"},K={class:"md:hidden"},X={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 rounded-lg mt-2"},z=(e,t)=>{const s=e.__vccOpts||e;for(const[a,l]of t)s[a]=l;return s},Q=z(s({__name:"AppHeader",setup(e){const t=a(!1),s=()=>{t.value=!t.value},g=()=>{t.value=!1};return(e,a)=>{const f=i("router-link");return p(),l("header",I,[n("nav",R,[n("div",$,[n("div",H,[o(f,{to:"/",class:"flex items-center space-x-2"},{default:c(()=>[n("div",W,[o(m(C),{class:"w-5 h-5 text-white"})]),a[0]||(a[0]=n("span",{class:"text-xl font-bold text-gray-900"},"LiveTV",-1))]),_:1,__:[0]})]),n("div",q,[n("div",G,[o(f,{to:"/",class:u(["nav-link",{"nav-link-active":"home"===e.$route.name}])},{default:c(()=>[o(m(P),{class:"w-4 h-4 mr-2"}),a[1]||(a[1]=v(" Home "))]),_:1,__:[1]},8,["class"]),o(f,{to:"/live",class:u(["nav-link",{"nav-link-active":"live"===e.$route.name}])},{default:c(()=>[o(m(j),{class:"w-4 h-4 mr-2"}),a[2]||(a[2]=v(" Live TV "))]),_:1,__:[2]},8,["class"]),o(f,{to:"/schedule",class:u(["nav-link",{"nav-link-active":"schedule"===e.$route.name}])},{default:c(()=>[o(m(D),{class:"w-4 h-4 mr-2"}),a[3]||(a[3]=v(" Schedule "))]),_:1,__:[3]},8,["class"]),o(f,{to:"/admin",class:u(["nav-link",{"nav-link-active":"admin"===e.$route.name}])},{default:c(()=>[o(m(U),{class:"w-4 h-4 mr-2"}),a[4]||(a[4]=v(" Admin "))]),_:1,__:[4]},8,["class"])])]),n("div",Y,[n("button",{onClick:s,class:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"},[t.value?(p(),d(m(L),{key:1,class:"w-6 h-6"})):(p(),d(m(A),{key:0,class:"w-6 h-6"}))])])]),r(n("div",K,[n("div",X,[o(f,{to:"/",class:"mobile-nav-link",onClick:g},{default:c(()=>[o(m(P),{class:"w-4 h-4 mr-2"}),a[5]||(a[5]=v(" Home "))]),_:1,__:[5]}),o(f,{to:"/live",class:"mobile-nav-link",onClick:g},{default:c(()=>[o(m(j),{class:"w-4 h-4 mr-2"}),a[6]||(a[6]=v(" Live TV "))]),_:1,__:[6]}),o(f,{to:"/schedule",class:"mobile-nav-link",onClick:g},{default:c(()=>[o(m(D),{class:"w-4 h-4 mr-2"}),a[7]||(a[7]=v(" Schedule "))]),_:1,__:[7]}),o(f,{to:"/admin",class:"mobile-nav-link",onClick:g},{default:c(()=>[o(m(U),{class:"w-4 h-4 mr-2"}),a[8]||(a[8]=v(" Admin "))]),_:1,__:[8]})])],512),[[h,t.value]])])])}}}),[["__scopeId","data-v-f0e57ffb"]]),Z={class:"bg-gray-900 text-white"},J={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},ee={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},te={class:"col-span-1 md:col-span-2"},se={class:"flex items-center space-x-2 mb-4"},ae={class:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"},le={class:"flex space-x-4"},ne={href:"#",class:"text-gray-400 hover:text-white transition-colors"},re={href:"#",class:"text-gray-400 hover:text-white transition-colors"},oe={href:"#",class:"text-gray-400 hover:text-white transition-colors"},ie={href:"#",class:"text-gray-400 hover:text-white transition-colors"},ce={class:"space-y-2"},ue={class:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center"},de={class:"text-gray-400 text-sm"},me={class:"flex items-center space-x-4 mt-4 md:mt-0"},he={class:"flex items-center space-x-2 text-sm text-gray-400"},pe=s({__name:"AppFooter",setup(e){const t=g(()=>(new Date).getFullYear()),s=a(1247);return setInterval(()=>{s.value=Math.floor(2e3*Math.random())+500},3e4),(e,a)=>{const r=i("router-link");return p(),l("footer",Z,[n("div",J,[n("div",ee,[n("div",te,[n("div",se,[n("div",ae,[o(m(C),{class:"w-5 h-5 text-white"})]),a[0]||(a[0]=n("span",{class:"text-xl font-bold"},"LiveTV",-1))]),a[1]||(a[1]=n("p",{class:"text-gray-400 mb-4 max-w-md"}," Your ultimate destination for live sports streaming. Watch your favorite games and matches from around the world in high quality. ",-1)),n("div",le,[n("a",ne,[o(m(T),{class:"w-5 h-5"})]),n("a",re,[o(m(O),{class:"w-5 h-5"})]),n("a",oe,[o(m(V),{class:"w-5 h-5"})]),n("a",ie,[o(m(M),{class:"w-5 h-5"})])])]),n("div",null,[a[6]||(a[6]=n("h3",{class:"text-lg font-semibold mb-4"},"Quick Links",-1)),n("ul",ce,[n("li",null,[o(r,{to:"/",class:"text-gray-400 hover:text-white transition-colors"},{default:c(()=>a[2]||(a[2]=[v(" Home ")])),_:1,__:[2]})]),n("li",null,[o(r,{to:"/live",class:"text-gray-400 hover:text-white transition-colors"},{default:c(()=>a[3]||(a[3]=[v(" Live TV ")])),_:1,__:[3]})]),n("li",null,[o(r,{to:"/schedule",class:"text-gray-400 hover:text-white transition-colors"},{default:c(()=>a[4]||(a[4]=[v(" Schedule ")])),_:1,__:[4]})]),a[5]||(a[5]=n("li",null,[n("a",{href:"#",class:"text-gray-400 hover:text-white transition-colors"}," Sports ")],-1))])]),a[7]||(a[7]=f('<div><h3 class="text-lg font-semibold mb-4">Support</h3><ul class="space-y-2"><li><a href="#" class="text-gray-400 hover:text-white transition-colors"> Help Center </a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors"> Contact Us </a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors"> Privacy Policy </a></li><li><a href="#" class="text-gray-400 hover:text-white transition-colors"> Terms of Service </a></li></ul></div>',1))]),n("div",ue,[n("p",de," © "+x(t.value)+" LiveTV. All rights reserved. ",1),n("div",me,[n("div",he,[a[8]||(a[8]=n("div",{class:"w-2 h-2 bg-green-500 rounded-full animate-pulse"},null,-1)),n("span",null,x(s.value)+" users online",1)])])])])])}}}),ve={class:"min-h-screen flex flex-col bg-gray-50"},ge={class:"flex-1"},fe=s({__name:"App",setup:e=>(e,t)=>(p(),l("div",ve,[o(Q),n("main",ge,[o(m(b))]),o(pe)]))}),xe={},be=function(e,t,s){let a=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),l=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));a=e(t.map(e=>{if((e=function(e){return"/"+e}(e))in xe)return;xe[e]=!0;const t=e.endsWith(".css"),s=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${s}`))return;const a=document.createElement("link");return a.rel=t?"stylesheet":"modulepreload",t||(a.as="script"),a.crossOrigin="",a.href=e,l&&a.setAttribute("nonce",l),document.head.appendChild(a),t?new Promise((t,s)=>{a.addEventListener("load",t),a.addEventListener("error",()=>s(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function l(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then(t=>{for(const e of t||[])"rejected"===e.status&&l(e.reason);return e().catch(l)})};const we=new class{constructor(){t(this,"baseURL","https://topembed.pw/api.php"),F.defaults.timeout=15e3,F.defaults.headers.common.Accept="application/json",console.log("API Service initialized with URL:",this.baseURL)}async getLiveEvents(e){try{console.log("Fetching events from API:",this.baseURL);const t=await F.get(this.baseURL,{params:e?{date:e}:{},headers:{"Content-Type":"application/json"}});return console.log("API Response:",t.data),t.data}catch(t){return console.error("Error fetching events from API:",t),console.log("Using fallback mock data"),this.getMockData()}}getMockData(){return{events:{"2025-06-20":[{unix_timestamp:1750372800,sport:"Baseball",tournament:"MLB",match:"Miami Marlins - Philadelphia Phillies",channels:["https://topembed.pw/channel/FanDuelSportsFlorida[USA]","https://topembed.pw/channel/NBCSPhiladelphia[USA]"]},{unix_timestamp:1750373100,sport:"Football",tournament:"CONCACAF Gold Cup",match:"Trinidad and Tobago - Haiti",channels:["https://topembed.pw/channel/FOXSports1[USA]"]},{unix_timestamp:1750374900,sport:"Baseball",tournament:"MLB",match:"Atlanta Braves - New York Mets",channels:["https://topembed.pw/channel/FanDuelSportsSoutheast[USA]","https://topembed.pw/channel/MLBNetwork[USA]"]},{unix_timestamp:1750435200,sport:"Football",tournament:"FIFA Club World Cup 2025",match:"Benfica - Auckland City",channels:["https://topembed.pw/channel/DirecTVSports2[Argentina]","https://topembed.pw/channel/ProArena[Romania]","https://topembed.pw/channel/SuperSportLaLiga[SouthAfrica]","https://topembed.pw/channel/SuperSportPremierLeague[SouthAfrica]","https://topembed.pw/channel/ex7137796"]}],"2025-07-19":[{unix_timestamp:1752912e3,sport:"Sailing",tournament:"SailGP",match:"SailGP Portsmouth - Day 1",channels:["https://topembed.pw/channel/SkySportsAction[UK]"]}],"2025-07-20":[{unix_timestamp:1752998400,sport:"Sailing",tournament:"SailGP",match:"SailGP Portsmouth - Day 2",channels:["https://topembed.pw/channel/SkySportsAction[UK]"]}]}}}async getChannels(){return await new Promise(e=>setTimeout(e,500)),[{id:"1",name:"ESPN",url:"https://topembed.pw/channel/ESPN[USA]",category:"Sports",isLive:!0},{id:"2",name:"FOX Sports",url:"https://topembed.pw/channel/FOXSports[USA]",category:"Sports",isLive:!0},{id:"3",name:"NBC Sports",url:"https://topembed.pw/channel/NBCSports[USA]",category:"Sports",isLive:!0}]}getChannelName(e){const t=e.match(/\/channel\/([^[]+)/);if(t){let e=t[1];return e.startsWith("ex")?`Channel ${e.substring(2)}`:e.replace(/([A-Z])/g," $1").trim()}return"Unknown Channel"}getChannelRegion(e){const t=e.match(/\[([^\]]+)\]/);return t?t[1]:"Global"}getEventTime(e){return new Date(1e3*e).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})}getEventDate(e){return new Date(1e3*e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}getEventDateTime(e){return new Date(1e3*e).toLocaleString("en-US",{weekday:"short",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0})}isEventLive(e){const t=Date.now()/1e3;return t>=e&&t<=e+10800}getTimeUntilEvent(e){const t=e-Date.now()/1e3;if(t<0)return this.isEventLive(e)?"LIVE NOW":"Ended";const s=Math.floor(t/86400),a=Math.floor(t%86400/3600),l=Math.floor(t%3600/60);return s>0?`in ${s}d ${a}h`:a>0?`in ${a}h ${l}m`:l>0?`in ${l}m`:"Starting soon"}getSportIcon(e){return{Baseball:"⚾",Football:"⚽",Basketball:"🏀",Soccer:"⚽",Tennis:"🎾",Hockey:"🏒",Golf:"⛳",Boxing:"🥊",MMA:"🥊",Racing:"🏎️",Sailing:"⛵",Cricket:"🏏",Rugby:"🏉",Volleyball:"🏐"}[e]||"🏆"}getAllSports(e){const t=new Set;return Object.values(e).flat().forEach(e=>{t.add(e.sport)}),Array.from(t).sort()}getEventsByDate(e,t){return e[t]||[]}filterLiveEvents(e){const t=[];return Object.values(e).flat().forEach(e=>{this.isEventLive(e.unix_timestamp)&&t.push(e)}),t.sort((e,t)=>e.unix_timestamp-t.unix_timestamp)}getUpcomingEvents(e,t=10){const s=Date.now()/1e3,a=[];return Object.values(e).flat().forEach(e=>{e.unix_timestamp>s&&a.push(e)}),a.sort((e,t)=>e.unix_timestamp-t.unix_timestamp).slice(0,t)}},ye=w("livetv",()=>{const e=a({}),t=a([]),s=a(!1),l=a(null),n=a(null),r=a(null),o=g(()=>{const t=(new Date).toISOString().split("T")[0];return e.value[t]||[]}),i=g(()=>we.filterLiveEvents(e.value)),c=g(()=>we.getUpcomingEvents(e.value,5)),u=g(()=>we.getAllSports(e.value)),d=g(()=>t.value.filter(e=>e.isLive)),m=g(()=>Object.values(e.value).flat().length),h=g(()=>{const t=Object.keys(e.value).sort(),s={};return t.forEach(t=>{s[t]=e.value[t].sort((e,t)=>e.unix_timestamp-t.unix_timestamp)}),s});async function p(t){s.value=!0,l.value=null;try{const s=await we.getLiveEvents(t);e.value=s.events}catch(a){l.value="Failed to fetch live events",console.error("Error fetching events:",a)}finally{s.value=!1}}async function v(){s.value=!0,l.value=null;try{const e=await we.getChannels();t.value=e}catch(e){l.value="Failed to fetch channels",console.error("Error fetching channels:",e)}finally{s.value=!1}}return{events:e,channels:t,loading:s,error:l,selectedEvent:n,selectedChannel:r,todayEvents:o,liveEvents:i,upcomingEvents:c,allSports:u,liveChannels:d,totalEvents:m,eventsByDate:h,fetchEvents:p,fetchChannels:v,selectEvent:function(e){n.value=e,e.channels.length>0&&(r.value=e.channels[0])},selectChannel:function(e){r.value=e},clearSelection:function(){n.value=null,r.value=null},getEventsBySport:function(t){const s={};return Object.entries(e.value).forEach(([e,a])=>{const l=a.filter(e=>e.sport===t);l.length>0&&(s[e]=l)}),s},getEventsForDate:function(t){return we.getEventsByDate(e.value,t)},refreshData:function(){return Promise.all([p(),v()])}}}),_e={class:"relative bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white overflow-hidden"},Se={class:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32"},Ee={class:"text-center"},ke={class:"flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up"},Ae={class:"py-16 bg-white"},Le={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ce={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Pe={class:"text-center"},je={class:"text-3xl font-bold text-gray-900 mb-2"},De={class:"text-center"},Ue={class:"inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4"},Te={class:"text-3xl font-bold text-gray-900 mb-2"},Oe={class:"text-center"},Ve={class:"inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4"},Me={class:"text-3xl font-bold text-gray-900 mb-2"},Fe={class:"text-center"},Be={class:"inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4"},Ne={class:"text-3xl font-bold text-gray-900 mb-2"},Ie={class:"py-20 bg-gray-50"},Re={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},$e={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},He={class:"bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"},We={class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6"},qe={class:"bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"},Ge={class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6"},Ye={class:"bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"},Ke={class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6"},Xe=s({__name:"HomeView",setup(e){const t=ye(),s=a({liveEvents:0,totalChannels:0,activeUsers:1247,totalEvents:0});return y(async()=>{await t.fetchEvents(),await t.fetchChannels(),s.value.liveEvents=t.liveEvents.length,s.value.totalChannels=t.channels.length,s.value.totalEvents=t.totalEvents,setInterval(()=>{s.value.liveEvents=t.liveEvents.length,s.value.activeUsers=Math.floor(2e3*Math.random())+800},3e4)}),(e,t)=>{const a=i("router-link");return p(),l("div",null,[n("section",_e,[t[4]||(t[4]=n("div",{class:"absolute inset-0 bg-black opacity-50"},null,-1)),t[5]||(t[5]=n("div",{class:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"},null,-1)),n("div",Se,[n("div",Ee,[t[2]||(t[2]=n("h1",{class:"text-4xl md:text-6xl font-bold mb-6 animate-fade-in"},[v(" Watch Live Sports "),n("span",{class:"block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400"}," Anywhere, Anytime ")],-1)),t[3]||(t[3]=n("p",{class:"text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto animate-slide-up"}," Stream your favorite sports events in HD quality. Never miss a game with our comprehensive live TV coverage. ",-1)),n("div",ke,[o(a,{to:"/live",class:"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg"},{default:c(()=>[o(m(N),{class:"w-5 h-5 mr-2"}),t[0]||(t[0]=v(" Watch Live Now "))]),_:1,__:[0]}),o(a,{to:"/schedule",class:"inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-lg hover:bg-white/20 transition-all duration-200 border border-white/20"},{default:c(()=>[o(m(D),{class:"w-5 h-5 mr-2"}),t[1]||(t[1]=v(" View Schedule "))]),_:1,__:[1]})])])]),t[6]||(t[6]=n("div",{class:"absolute top-20 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse-slow"},null,-1)),t[7]||(t[7]=n("div",{class:"absolute bottom-20 right-10 w-32 h-32 bg-purple-500/20 rounded-full animate-pulse-slow"},null,-1))]),n("section",Ae,[n("div",Le,[n("div",Ce,[n("div",Pe,[t[8]||(t[8]=n("div",{class:"inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4"},[n("div",{class:"w-3 h-3 bg-red-500 rounded-full animate-pulse"})],-1)),n("h3",je,x(s.value.liveEvents),1),t[9]||(t[9]=n("p",{class:"text-gray-600"},"Live Events Now",-1))]),n("div",De,[n("div",Ue,[o(m(D),{class:"w-8 h-8 text-purple-600"})]),n("h3",Te,x(s.value.totalEvents),1),t[10]||(t[10]=n("p",{class:"text-gray-600"},"Total Events",-1))]),n("div",Oe,[n("div",Ve,[o(m(C),{class:"w-8 h-8 text-blue-600"})]),n("h3",Me,x(s.value.totalChannels)+"+",1),t[11]||(t[11]=n("p",{class:"text-gray-600"},"Available Channels",-1))]),n("div",Fe,[n("div",Be,[o(m(B),{class:"w-8 h-8 text-green-600"})]),n("h3",Ne,x(s.value.activeUsers.toLocaleString()),1),t[12]||(t[12]=n("p",{class:"text-gray-600"},"Active Users",-1))])])])]),n("section",Ie,[n("div",Re,[t[19]||(t[19]=n("div",{class:"text-center mb-16"},[n("h2",{class:"text-3xl md:text-4xl font-bold text-gray-900 mb-4"}," Why Choose LiveTV? "),n("p",{class:"text-xl text-gray-600 max-w-2xl mx-auto"}," Experience the best in live sports streaming with our premium features ")],-1)),n("div",$e,[n("div",He,[n("div",We,[o(m(N),{class:"w-6 h-6 text-blue-600"})]),t[13]||(t[13]=n("h3",{class:"text-xl font-semibold text-gray-900 mb-4"},"HD Quality Streaming",-1)),t[14]||(t[14]=n("p",{class:"text-gray-600"}," Enjoy crystal clear HD quality streams with minimal buffering and maximum reliability. ",-1))]),n("div",qe,[n("div",Ge,[o(m(D),{class:"w-6 h-6 text-green-600"})]),t[15]||(t[15]=n("h3",{class:"text-xl font-semibold text-gray-900 mb-4"},"Complete Schedule",-1)),t[16]||(t[16]=n("p",{class:"text-gray-600"}," Never miss a game with our comprehensive schedule covering all major sports events. ",-1))]),n("div",Ye,[n("div",Ke,[o(m(C),{class:"w-6 h-6 text-purple-600"})]),t[17]||(t[17]=n("h3",{class:"text-xl font-semibold text-gray-900 mb-4"},"Multiple Channels",-1)),t[18]||(t[18]=n("p",{class:"text-gray-600"}," Access multiple streaming sources for each event to ensure you never miss the action. ",-1))])])])])])}}}),ze=_({history:S("/"),routes:[{path:"/",name:"home",component:Xe},{path:"/live",name:"live",component:()=>be(()=>import("./LiveView-CBLP0BYv.js"),__vite__mapDeps([0,1,2,3]))},{path:"/schedule",name:"schedule",component:()=>be(()=>import("./ScheduleView-DZ9ZeAtX.js"),__vite__mapDeps([4,1,2]))},{path:"/admin",name:"admin",component:()=>be(()=>import("./AdminView-C0yG8DH6.js"),__vite__mapDeps([5,2,1]))},{path:"/about",name:"about",component:()=>be(()=>import("./AboutView-BcoS1ILR.js"),__vite__mapDeps([6,1,2,7]))}]}),Qe=E(fe);Qe.use(k()),Qe.use(ze),Qe.mount("#app");export{z as _,we as a,ye as u};
