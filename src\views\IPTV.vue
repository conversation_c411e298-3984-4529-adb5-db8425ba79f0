<template>
  <div class="min-h-screen bg-black text-white">
    <section
      class="relative py-8 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 border-b border-white/10">
      <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col gap-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <h1
              class="text-3xl md:text-4xl font-black bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent">
              IPTV Channel Search
            </h1>
          </div>
          <div class="relative">
            <input v-model="searchQuery" @keyup.enter="searchChannels" type="text" placeholder="Search for a channel..."
              class="pl-4 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 transition-all duration-300 w-full" />
            <button @click="searchChannels" :disabled="loading"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold rounded-xl hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-purple-500/50 disabled:opacity-50 disabled:cursor-not-allowed">
              Search
            </button>
          </div>
        </div>
      </div>
    </section>

    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden p-6">
        <div v-if="loading" class="text-center py-8">
          <span class="text-lg font-bold animate-pulse">Searching...</span>
        </div>
        <div v-else-if="errorMessage" class="text-center py-8">
          <span class="text-red-400 font-bold">{{ errorMessage }}</span>
        </div>
        <div v-else-if="searchResults && Object.keys(searchResults).length === 0 && hasSearched"
          class="text-center py-8">
          <span class="text-gray-400">No channels found.</span>
        </div>
        <div v-else-if="searchResults && Object.keys(searchResults).length > 0">
          <div class="space-y-4">
            <div v-for="(item, id) in searchResults" :key="id"
              class="group relative bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 hover:border-purple-500/30 hover:bg-white/20 transition-all duration-300 cursor-pointer"
              @click="selectChannel(id, item.title)">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="font-bold text-white text-sm mb-1 line-clamp-2">{{ item.title }}</h4>
                  <p class="text-xs text-gray-400">ID: {{ id }}</p>
                </div>
                <div v-if="selectedId === id && streamUrl" class="flex items-center gap-2">
                  <button @click.stop="copyStreamUrl"
                    class="px-3 py-1 text-xs rounded bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:scale-105">Copy
                    Link</button>
                  <span class="text-green-400 text-xs" v-if="copied">Copied!</span>
                </div>
              </div>
              <div v-if="selectedId === id && streamLoading" class="mt-2 text-purple-300 text-xs">Loading stream link...
              </div>
              <div v-if="selectedId === id && streamError" class="mt-2 text-red-400 text-xs">{{ streamError }}</div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-400">
          <span>Type a channel name and search to get started.</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchQuery = ref('')
const searchResults = ref(null)
const loading = ref(false)
const errorMessage = ref('')
const hasSearched = ref(false)

const selectedId = ref(null)
const selectedTitle = ref('')
const streamUrl = ref('')
const streamLoading = ref(false)
const streamError = ref('')
const copied = ref(false)

async function searchChannels() {
  if (!searchQuery.value.trim()) return
  loading.value = true
  errorMessage.value = ''
  hasSearched.value = false
  searchResults.value = null
  selectedId.value = null
  streamUrl.value = ''
  streamError.value = ''
  try {
    const url = `https://searchtv.net/search/?query=${encodeURIComponent(searchQuery.value)}`
    const proxy = `https://corsproxy.io/?${encodeURIComponent(url)}`
    const res = await fetch(proxy)
    if (!res.ok) throw new Error('Failed to fetch search results')
    const data = await res.json()
    searchResults.value = data
    hasSearched.value = true
  } catch (e) {
    errorMessage.value = 'Error fetching search results.'
  } finally {
    loading.value = false
  }
}

async function selectChannel(id, title) {
  if (selectedId.value === id && streamUrl.value) return // already loaded
  selectedId.value = id
  selectedTitle.value = title
  streamUrl.value = ''
  streamError.value = ''
  copied.value = false
  streamLoading.value = true
  try {

    const url = `https://searchtv.net/stream/uuid/${id}/`
    const proxy = `https://corsproxy.io/?${encodeURIComponent(url)}`
    const res = await fetch(proxy)
    if (!res.ok) throw new Error('Failed to fetch stream link')
    const text = await res.text()
    // Parse the M3U response for the first http link
    const match = text.match(/http[^\s]+/)
    if (match) {
      streamUrl.value = match[0]
    } else {
      streamError.value = 'No stream link found.'
    }
  } catch (e) {
    streamError.value = 'Error fetching stream link.'
  } finally {
    streamLoading.value = false
  }
}

async function copyStreamUrl() {
  if (!streamUrl.value) return
  try {
    await navigator.clipboard.writeText(streamUrl.value)
    copied.value = true
    setTimeout(() => (copied.value = false), 1500)
  } catch (e) {
    streamError.value = 'Failed to copy link.'
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
