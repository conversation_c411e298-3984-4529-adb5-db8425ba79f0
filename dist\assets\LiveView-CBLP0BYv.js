import{d as e,r as t,j as s,m as l,c as a,a as n,i as r,t as i,w as o,b as c,u as d,y as u,z as v,F as m,A as g,n as x,B as p,o as h}from"./vendor-zrHeai00.js";import{u as y,a as f,_ as b}from"./index-B7BpRN8I.js";import{c as w,d as k,T as C,e as E,P as j}from"./utils-mFY9R7nY.js";const _={class:"min-h-screen bg-gray-50"},L={class:"bg-white shadow-sm"},V={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},S={class:"flex items-center justify-between"},T={class:"flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4"},I={class:"flex items-center space-x-4 text-sm text-gray-600"},N={class:"text-red-600 font-medium"},A={class:"flex items-center space-x-3"},R={class:"relative"},F=["value"],U=["disabled"],z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},B={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},P={class:"lg:col-span-1"},W={class:"bg-white rounded-lg shadow-sm"},q={class:"p-6 border-b border-gray-200"},D={class:"text-lg font-semibold text-gray-900"},G={class:"max-h-96 overflow-y-auto"},H={key:0,class:"p-6"},J={class:"animate-pulse space-y-4"},K={key:1,class:"p-6 text-center text-gray-500"},M={key:2,class:"divide-y divide-gray-200"},O=["onClick"],Q={class:"flex items-start justify-between"},X={class:"flex-1"},Y={class:"flex items-center space-x-2 mb-1"},Z={class:"text-xs text-gray-500"},$={class:"font-medium text-gray-900 text-sm"},ee={class:"text-xs text-gray-600 mt-1"},te={class:"text-xs text-blue-600 mt-1"},se={class:"lg:col-span-2"},le={class:"bg-white rounded-lg shadow-sm"},ae={key:0,class:"aspect-video bg-gray-900 rounded-t-lg flex items-center justify-center"},ne={class:"text-center text-white"},re={key:1},ie={class:"aspect-video bg-black rounded-t-lg relative"},oe=["src"],ce={key:1,class:"w-full h-full flex items-center justify-center text-white"},de={class:"text-center"},ue={class:"p-6"},ve={class:"flex items-center justify-between mb-4"},me={class:"text-xl font-bold text-gray-900"},ge={class:"text-gray-600"},xe={key:0},pe={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"},he=["onClick"],ye={class:"font-medium"},fe={class:"text-xs opacity-75 mt-1"},be={key:1,class:"bg-gray-50 rounded-lg p-3"},we={class:"flex items-center space-x-2"},ke={class:"font-medium text-gray-900"},Ce={class:"text-sm text-gray-600"},Ee=b(e({__name:"LiveView",setup(e){const b=y(),Ee=t(""),je=t(""),_e=s(()=>b.loading),Le=s(()=>b.todayEvents),Ve=s(()=>b.liveEvents),Se=s(()=>b.totalEvents),Te=s(()=>b.allSports),Ie=s(()=>b.selectedEvent),Ne=s(()=>b.selectedChannel),Ae=s(()=>{let e=Le.value;if(je.value&&(e=e.filter(e=>e.sport===je.value)),Ee.value){const t=Ee.value.toLowerCase();e=e.filter(e=>e.match.toLowerCase().includes(t)||e.sport.toLowerCase().includes(t)||e.tournament.toLowerCase().includes(t))}return e}),Re=async()=>{await b.fetchEvents()};return l(async()=>{await b.fetchEvents(),await b.fetchChannels()}),(e,t)=>(h(),a("div",_,[n("div",L,[n("div",V,[n("div",S,[t[4]||(t[4]=n("div",null,[n("h1",{class:"text-2xl font-bold text-gray-900 flex items-center"},[n("div",{class:"live-indicator mr-3"}),r(" Live TV ")]),n("p",{class:"text-gray-600 mt-1"},"Watch live sports events now")],-1)),n("div",T,[n("div",I,[n("span",null,i(Ae.value.length)+" events",1),n("span",N,i(Ve.value.length)+" live now",1),n("span",null,i(Se.value)+" total events",1)]),n("div",A,[n("div",R,[c(d(w),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500"}),o(n("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>Ee.value=e),type:"text",placeholder:"Search events...",class:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64 bg-white text-gray-900 placeholder-gray-500 font-medium",style:{"background-color":"white !important",color:"#1f2937 !important","font-weight":"500"}},null,512),[[u,Ee.value]])]),o(n("select",{"onUpdate:modelValue":t[1]||(t[1]=e=>je.value=e),class:"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-medium",style:{"background-color":"white !important",color:"#1f2937 !important"}},[t[2]||(t[2]=n("option",{value:"",style:{"background-color":"white !important",color:"#1f2937 !important","font-weight":"600"}},"All Sports ",-1)),(h(!0),a(m,null,g(Te.value,e=>(h(),a("option",{key:e,value:e,style:{"background-color":"white !important",color:"#1f2937 !important","font-weight":"600"}},i(d(f).getSportIcon(e))+" "+i(e),9,F))),128))],512),[[v,je.value]]),n("button",{onClick:Re,disabled:_e.value,class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"},[c(d(k),{class:x(["w-4 h-4 mr-2",{"animate-spin":_e.value}])},null,8,["class"]),t[3]||(t[3]=r(" Refresh "))],8,U)])])])])]),n("div",z,[n("div",B,[n("div",P,[n("div",W,[n("div",q,[n("h2",D,i(Ee.value||je.value?"Filtered Events":"Today's Events"),1)]),n("div",G,[_e.value?(h(),a("div",H,[n("div",J,[(h(),a(m,null,g(3,e=>n("div",{key:e,class:"h-20 bg-gray-200 rounded"})),64))])])):0===Ae.value.length?(h(),a("div",K,[c(d(C),{class:"w-12 h-12 mx-auto mb-4 text-gray-300"}),t[5]||(t[5]=n("p",null,"No events found",-1)),t[6]||(t[6]=n("p",{class:"text-sm mt-2"},"Try adjusting your search or filter",-1))])):(h(),a("div",M,[(h(!0),a(m,null,g(Ae.value,e=>{var s;return h(),a("div",{key:e.unix_timestamp,onClick:t=>(e=>{b.selectEvent(e)})(e),class:x(["p-4 cursor-pointer hover:bg-gray-50 transition-colors",{"bg-blue-50 border-r-4 border-blue-500":(null==(s=Ie.value)?void 0:s.unix_timestamp)===e.unix_timestamp}])},[n("div",Q,[n("div",X,[n("div",Y,[t[7]||(t[7]=n("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"},[n("div",{class:"w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"}),r(" LIVE ")],-1)),n("span",Z,i(d(f).getEventTime(e.unix_timestamp)),1)]),n("h3",$,i(e.match),1),n("p",ee,i(e.tournament)+" • "+i(e.sport),1),n("p",te,i(e.channels.length)+" channel(s)",1)]),c(d(j),{class:"w-4 h-4 text-gray-400"})])],10,O)}),128))]))])])]),n("div",se,[n("div",le,[Ie.value?(h(),a("div",re,[n("div",ie,[Ne.value?(h(),a("iframe",{key:0,src:Ne.value,allow:"encrypted-media",width:"100%",height:"100%",scrolling:"no",frameborder:"0",allowfullscreen:"",class:"w-full h-full rounded-t-lg"},null,8,oe)):(h(),a("div",ce,[n("div",de,[c(d(E),{class:"w-12 h-12 mx-auto mb-4 text-yellow-400"}),t[10]||(t[10]=n("p",null,"No channel selected",-1))])]))]),n("div",ue,[n("div",ve,[n("div",null,[n("h2",me,i(Ie.value.match),1),n("p",ge,i(Ie.value.tournament)+" • "+i(Ie.value.sport),1)]),t[11]||(t[11]=n("div",{class:"flex items-center space-x-2"},[n("span",{class:"live-indicator text-sm font-medium"},"LIVE")],-1))]),Ie.value.channels.length>1?(h(),a("div",xe,[t[12]||(t[12]=n("h3",{class:"text-sm font-medium text-gray-900 mb-3"},"Available Channels:",-1)),n("div",pe,[(h(!0),a(m,null,g(Ie.value.channels,(e,t)=>(h(),a("button",{key:t,onClick:t=>(e=>{b.selectChannel(e)})(e),class:x(["p-3 text-sm rounded-lg border transition-colors text-left",Ne.value===e?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"])},[n("div",ye,i(d(f).getChannelName(e)),1),n("div",fe,i(d(f).getChannelRegion(e)),1)],10,he))),128))])])):1===Ie.value.channels.length?(h(),a("div",be,[n("div",we,[t[13]||(t[13]=n("div",{class:"w-2 h-2 bg-green-500 rounded-full"},null,-1)),n("div",null,[n("div",ke,i(d(f).getChannelName(Ie.value.channels[0])),1),n("div",Ce,i(d(f).getChannelRegion(Ie.value.channels[0])),1)])])])):p("",!0)])])):(h(),a("div",ae,[n("div",ne,[c(d(C),{class:"w-16 h-16 mx-auto mb-4 text-gray-300"}),t[8]||(t[8]=n("h3",{class:"text-xl font-semibold mb-2 text-white"},"Select an Event",-1)),t[9]||(t[9]=n("p",{class:"text-gray-300"},"Choose a live event from the list to start watching",-1))])]))])])])])]))}}),[["__scopeId","data-v-7e78140e"]]);export{Ee as default};
