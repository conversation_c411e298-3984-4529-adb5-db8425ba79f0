import{d as e,r as t,j as s,m as a,c as l,a as n,b as o,i,u as r,w as c,z as d,F as m,A as u,n as x,B as g,t as p,C as v,D as y,o as b}from"./vendor-zrHeai00.js";import{u as h,a as f}from"./index-B7BpRN8I.js";import{C as w,d as k,T as S,f as D,B as C,P as E}from"./utils-mFY9R7nY.js";const j={class:"min-h-screen bg-gray-50"},_={class:"bg-white shadow-sm"},T={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},B={class:"flex items-center justify-between"},L={class:"text-2xl font-bold text-gray-900 flex items-center"},U={class:"flex items-center space-x-4"},I=["value"],N=["disabled"],R={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},V={key:0,class:"space-y-4"},A={key:1,class:"space-y-6"},$={class:"bg-gray-50 px-6 py-4 border-b border-gray-200"},z={class:"text-lg font-semibold text-gray-900"},F={class:"text-sm text-gray-600"},O={class:"divide-y divide-gray-200"},P=["onClick"],W={class:"flex items-center justify-between"},q={class:"flex-1"},G={class:"flex items-center space-x-3 mb-2"},H={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},J={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"},K={class:"text-sm text-gray-500"},M={class:"text-sm font-medium text-gray-900"},Q={class:"text-lg font-semibold text-gray-900 mb-1"},X={class:"flex items-center space-x-4 text-sm text-gray-600"},Y={class:"flex items-center"},Z={class:"flex items-center"},ee={class:"flex items-center space-x-3"},te=["onClick"],se=["onClick"],ae={key:0,class:"text-center py-12"},le={class:"text-gray-600"},ne=e({__name:"ScheduleView",setup(e){const ne=y(),oe=h(),ie=t(""),re=s(()=>oe.loading);s(()=>oe.events);const ce=s(()=>ie.value?oe.getEventsBySport(ie.value):oe.eventsByDate),de=s(()=>oe.allSports),me=s(()=>ie.value?`No ${ie.value.toLowerCase()} events scheduled`:"No events scheduled for the selected period"),ue=e=>{const t=new Date(String(e)),s=new Date,a=new Date(s);return a.setDate(a.getDate()+1),t.toDateString()===s.toDateString()?"Today":t.toDateString()===a.toDateString()?"Tomorrow":t.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})},xe=async()=>{await oe.fetchEvents()},ge=e=>{oe.selectEvent(e),ne.push("/live")};return a(async()=>{await oe.fetchEvents()}),(e,t)=>(b(),l("div",j,[n("div",_,[n("div",T,[n("div",B,[n("div",null,[n("h1",L,[o(r(w),{class:"w-6 h-6 mr-3 text-blue-600"}),t[1]||(t[1]=i(" Event Schedule "))]),t[2]||(t[2]=n("p",{class:"text-gray-600 mt-1"},"Upcoming sports events and matches",-1))]),n("div",U,[c(n("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>ie.value=e),class:"px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900 font-medium",style:{"background-color":"white !important",color:"#1f2937 !important"}},[t[3]||(t[3]=n("option",{value:"",style:{"background-color":"white !important",color:"#1f2937 !important","font-weight":"600"}}," All Sports",-1)),(b(!0),l(m,null,u(de.value,e=>(b(),l("option",{key:e,value:e,style:{"background-color":"white !important",color:"#1f2937 !important","font-weight":"600"}},p(r(f).getSportIcon(e))+" "+p(e),9,I))),128))],512),[[d,ie.value]]),n("button",{onClick:xe,disabled:re.value,class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"},[o(r(k),{class:x(["w-4 h-4 mr-2",{"animate-spin":re.value}])},null,8,["class"]),t[4]||(t[4]=i(" Refresh "))],8,N)])])])]),n("div",R,[re.value?(b(),l("div",V,[(b(),l(m,null,u(5,e=>n("div",{key:e,class:"bg-white rounded-lg shadow-sm p-6"},t[5]||(t[5]=[n("div",{class:"animate-pulse"},[n("div",{class:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),n("div",{class:"h-6 bg-gray-200 rounded w-3/4 mb-2"}),n("div",{class:"h-4 bg-gray-200 rounded w-1/2"})],-1)]))),64))])):(b(),l("div",A,[(b(!0),l(m,null,u(ce.value,(e,s)=>(b(),l("div",{key:s,class:"bg-white rounded-lg shadow-sm overflow-hidden"},[n("div",$,[n("h2",z,p(ue(s)),1),n("p",F,p(e.length)+" event(s)",1)]),n("div",O,[(b(!0),l(m,null,u(e,e=>(b(),l("div",{key:e.unix_timestamp,class:"p-6 hover:bg-gray-50 transition-colors cursor-pointer",onClick:t=>ge(e)},[n("div",W,[n("div",q,[n("div",G,[n("span",H,p(r(f).getSportIcon(e.sport))+" "+p(e.sport),1),r(f).isEventLive(e.unix_timestamp)?(b(),l("span",J,t[6]||(t[6]=[n("div",{class:"w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"},null,-1),i(" LIVE ")]))):g("",!0),n("span",K,p(e.tournament),1),n("span",M,p(r(f).getEventTime(e.unix_timestamp)),1)]),n("h3",Q,p(e.match),1),n("div",X,[n("div",Y,[o(r(S),{class:"w-4 h-4 mr-1"}),i(" "+p(e.channels.length)+" channel(s) ",1)]),n("div",Z,[o(r(D),{class:"w-4 h-4 mr-1"}),i(" "+p(r(f).getTimeUntilEvent(e.unix_timestamp)),1)])])]),n("div",ee,[n("button",{onClick:v(t=>(e=>{alert(`Reminder set for ${e.match}`)})(e),["stop"]),class:"inline-flex items-center px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"},[o(r(C),{class:"w-4 h-4 mr-1"}),t[7]||(t[7]=i(" Remind "))],8,te),n("button",{onClick:v(t=>ge(e),["stop"]),class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"},[o(r(E),{class:"w-4 h-4 mr-1"}),t[8]||(t[8]=i(" Watch "))],8,se)])])],8,P))),128))])]))),128)),0===Object.keys(ce.value).length?(b(),l("div",ae,[o(r(w),{class:"w-16 h-16 mx-auto text-gray-300 mb-4"}),t[9]||(t[9]=n("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"No events found",-1)),n("p",le,p(me.value),1)])):g("",!0)]))])]))}});export{ne as default};
