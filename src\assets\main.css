@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import './base.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.live-indicator {
  animation: pulse 2s infinite;
}

.live-indicator::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dropdown styling for better visibility */
select {
  background-color: white !important;
  color: #1f2937 !important;
}

select option {
  background-color: white !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
  padding: 8px 12px !important;
}

select option:hover {
  background-color: #f3f4f6 !important;
  color: #1f2937 !important;
}

select option:checked {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Input field styling for better visibility */
input[type="text"], input[type="search"] {
  background-color: white !important;
  color: #1f2937 !important;
  font-weight: 500 !important;
}

input[type="text"]::placeholder, input[type="search"]::placeholder {
  color: #6b7280 !important;
  font-weight: 400 !important;
}

input[type="text"]:focus, input[type="search"]:focus {
  background-color: white !important;
  color: #1f2937 !important;
  outline: none !important;
}

/* Responsive improvements for Schedule page */
@media (max-width: 640px) {
  .schedule-card {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .schedule-header {
    padding: 1rem;
  }

  .event-tags {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .event-title {
    font-size: 1rem;
    line-height: 1.4;
  }

  .action-buttons {
    width: 100%;
  }

  .action-buttons button {
    flex: 1;
    min-width: 0;
  }
}

/* Better touch targets for mobile */
@media (max-width: 768px) {
  .event-card {
    min-height: 44px;
  }

  .event-card button {
    min-height: 44px;
    padding: 0.75rem 1rem;
  }

  .sport-filter select {
    min-height: 44px;
  }
}
