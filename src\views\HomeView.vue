<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { Play, Users, Tv, Calendar, Star, ArrowRight } from 'lucide-vue-next'

const store = useLiveTvStore()
const stats = ref({
  liveEvents: 0,
  totalChannels: 0,
  activeUsers: 1247,
  totalEvents: 0
})

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()

  stats.value.liveEvents = store.liveEvents.length
  stats.value.totalChannels = store.channels.length
  stats.value.totalEvents = store.totalEvents

  // Update stats every 30 seconds
  setInterval(() => {
    stats.value.liveEvents = store.liveEvents.length
    stats.value.activeUsers = Math.floor(Math.random() * 2000) + 800
  }, 30000)
})
</script>

<template>
  <div>
    <!-- Hero Section -->
    <section class="relative text-white overflow-hidden min-h-screen flex items-center">
      <!-- Cricket Background Image with Enhanced Effects -->
      <div class="absolute inset-0 bg-cover bg-center bg-no-repeat cricket-hero-bg"
        style="background-image: url('/cricbackground.jpg'); filter: brightness(0.8) contrast(1.1);">
      </div>

      <!-- Enhanced Overlay for better text readability -->
      <div class="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/60"></div>
      <div class="absolute inset-0 bg-gradient-to-t from-blue-900/40 via-transparent to-purple-900/30"></div>

      <!-- Animated overlay effects -->
      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse-slow">
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
          <!-- Enhanced Title with Cricket Theme -->
          <div class="mb-8 shimmer-effect">
            <h1 class="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 animate-fade-in leading-tight hero-text-shadow">
              <span class="block text-white drop-shadow-2xl animate-float">Watch Live</span>
              <span
                class="block text-transparent bg-clip-text bg-gradient-to-r from-orange-400 via-yellow-400 to-red-400 drop-shadow-lg animate-glow">
                🏏 Cricket & Sports
              </span>
              <span class="block text-2xl md:text-3xl lg:text-4xl font-semibold text-blue-200 mt-4 drop-shadow-lg">
                Anywhere, Anytime ⚡
              </span>
            </h1>
          </div>

          <p
            class="text-xl md:text-2xl lg:text-3xl text-gray-200 mb-12 max-w-4xl mx-auto animate-slide-up leading-relaxed drop-shadow-lg">
            Experience the thrill of live cricket and sports in stunning HD quality.
            <span class="block mt-2 text-lg md:text-xl text-blue-200">
              Never miss a boundary, wicket, or winning moment with our premium streaming service.
            </span>
          </p>

          <!-- Enhanced Call-to-Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center animate-slide-up">
            <router-link to="/live"
              class="group inline-flex items-center px-10 py-5 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white font-bold text-lg rounded-xl hover:from-orange-600 hover:via-red-600 hover:to-pink-600 transform hover:scale-110 transition-all duration-300 shadow-2xl hover:shadow-orange-500/50 border-2 border-white/20 glow-button animate-glow">
              <Play class="w-6 h-6 mr-3 group-hover:animate-pulse" />
              🏏 Watch Live Cricket
            </router-link>

            <router-link to="/schedule"
              class="group inline-flex items-center px-10 py-5 bg-white/15 backdrop-blur-md text-white font-bold text-lg rounded-xl hover:bg-white/25 transition-all duration-300 border-2 border-white/30 hover:border-white/50 shadow-xl hover:shadow-2xl transform hover:scale-105 animate-float">
              <Calendar class="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-300" />
              📅 Match Schedule
            </router-link>
          </div>

          <!-- Additional Cricket-themed Elements -->
          <div class="mt-16 flex justify-center items-center space-x-8 opacity-80">
            <div class="text-center">
              <div class="text-3xl mb-2">🏏</div>
              <p class="text-sm text-gray-300">Live Cricket</p>
            </div>
            <div class="text-center">
              <div class="text-3xl mb-2">📺</div>
              <p class="text-sm text-gray-300">HD Streaming</p>
            </div>
            <div class="text-center">
              <div class="text-3xl mb-2">⚡</div>
              <p class="text-sm text-gray-300">Fast & Reliable</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Floating Elements with Cricket Theme -->
      <div
        class="absolute top-20 left-10 w-24 h-24 bg-orange-500/30 rounded-full animate-pulse-slow backdrop-blur-sm border border-white/20">
      </div>
      <div
        class="absolute bottom-20 right-10 w-32 h-32 bg-red-500/25 rounded-full animate-pulse-slow backdrop-blur-sm border border-white/20">
      </div>
      <div
        class="absolute top-1/3 right-20 w-16 h-16 bg-yellow-500/30 rounded-full animate-pulse-slow backdrop-blur-sm border border-white/20">
      </div>
      <div
        class="absolute bottom-1/3 left-20 w-20 h-20 bg-blue-500/25 rounded-full animate-pulse-slow backdrop-blur-sm border border-white/20">
      </div>

      <!-- Cricket Ball Animation -->
      <div
        class="absolute top-1/4 left-1/4 w-8 h-8 bg-red-600 rounded-full cricket-ball-animation opacity-60 shadow-lg border-2 border-white/30">
      </div>
      <div
        class="absolute bottom-1/4 right-1/4 w-6 h-6 bg-white rounded-full animate-cricket-spin opacity-70 shadow-lg border border-red-300">
      </div>
      <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-yellow-400 rounded-full animate-float opacity-80 shadow-lg">
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.liveEvents }}</h3>
            <p class="text-gray-600">Live Events Now</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
              <Calendar class="w-8 h-8 text-purple-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.totalEvents }}</h3>
            <p class="text-gray-600">Total Events</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Tv class="w-8 h-8 text-blue-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.totalChannels }}+</h3>
            <p class="text-gray-600">Available Channels</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <Users class="w-8 h-8 text-green-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.activeUsers.toLocaleString() }}</h3>
            <p class="text-gray-600">Active Users</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose LiveTV?
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the best in live sports streaming with our premium features
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
              <Play class="w-6 h-6 text-blue-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">HD Quality Streaming</h3>
            <p class="text-gray-600">
              Enjoy crystal clear HD quality streams with minimal buffering and maximum reliability.
            </p>
          </div>

          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
              <Calendar class="w-6 h-6 text-green-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Complete Schedule</h3>
            <p class="text-gray-600">
              Never miss a game with our comprehensive schedule covering all major sports events.
            </p>
          </div>

          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
              <Tv class="w-6 h-6 text-purple-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Multiple Channels</h3>
            <p class="text-gray-600">
              Access multiple streaming sources for each event to ensure you never miss the action.
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
