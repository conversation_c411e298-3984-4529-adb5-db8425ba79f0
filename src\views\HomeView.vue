<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useLiveTvStore } from '@/stores/counter'
import { Play, Users, Tv, Calendar, Star, ArrowRight } from 'lucide-vue-next'

const store = useLiveTvStore()
const stats = ref({
  liveEvents: 0,
  totalChannels: 0,
  activeUsers: 1247,
  totalEvents: 0
})

onMounted(async () => {
  await store.fetchEvents()
  await store.fetchChannels()

  stats.value.liveEvents = store.liveEvents.length
  stats.value.totalChannels = store.channels.length
  stats.value.totalEvents = store.totalEvents

  // Update stats every 30 seconds
  setInterval(() => {
    stats.value.liveEvents = store.liveEvents.length
    stats.value.activeUsers = Math.floor(Math.random() * 2000) + 800
  }, 30000)
})
</script>

<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-50"></div>
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
            Watch Live Sports
            <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              Anywhere, Anytime
            </span>
          </h1>

          <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto animate-slide-up">
            Stream your favorite sports events in HD quality. Never miss a game with our comprehensive live TV coverage.
          </p>

          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
            <router-link to="/live"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
              <Play class="w-5 h-5 mr-2" />
              Watch Live Now
            </router-link>

            <router-link to="/schedule"
              class="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-lg hover:bg-white/20 transition-all duration-200 border border-white/20">
              <Calendar class="w-5 h-5 mr-2" />
              View Schedule
            </router-link>
          </div>
        </div>
      </div>

      <!-- Floating Elements -->
      <div class="absolute top-20 left-10 w-20 h-20 bg-blue-500/20 rounded-full animate-pulse-slow"></div>
      <div class="absolute bottom-20 right-10 w-32 h-32 bg-purple-500/20 rounded-full animate-pulse-slow"></div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.liveEvents }}</h3>
            <p class="text-gray-600">Live Events Now</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
              <Calendar class="w-8 h-8 text-purple-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.totalEvents }}</h3>
            <p class="text-gray-600">Total Events</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              <Tv class="w-8 h-8 text-blue-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.totalChannels }}+</h3>
            <p class="text-gray-600">Available Channels</p>
          </div>

          <div class="text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <Users class="w-8 h-8 text-green-600" />
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-2">{{ stats.activeUsers.toLocaleString() }}</h3>
            <p class="text-gray-600">Active Users</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose LiveTV?
          </h2>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Experience the best in live sports streaming with our premium features
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
              <Play class="w-6 h-6 text-blue-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">HD Quality Streaming</h3>
            <p class="text-gray-600">
              Enjoy crystal clear HD quality streams with minimal buffering and maximum reliability.
            </p>
          </div>

          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
              <Calendar class="w-6 h-6 text-green-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Complete Schedule</h3>
            <p class="text-gray-600">
              Never miss a game with our comprehensive schedule covering all major sports events.
            </p>
          </div>

          <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
              <Tv class="w-6 h-6 text-purple-600" />
            </div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Multiple Channels</h3>
            <p class="text-gray-600">
              Access multiple streaming sources for each event to ensure you never miss the action.
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
