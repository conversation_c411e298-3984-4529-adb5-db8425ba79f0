{"name": "livetv", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "deploy:netlify": "npm run build && netlify deploy --prod --dir=dist", "deploy:vercel": "npm run build && vercel --prod", "deploy:gh-pages": "npm run build && gh-pages -d dist", "serve": "npm run build && npm run preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "lucide-vue-next": "^0.518.0", "pinia": "^3.0.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}