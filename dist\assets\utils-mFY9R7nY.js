import{h as e}from"./vendor-zrHeai00.js";
/**
 * @license lucide-vue-next v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()))(e);return t.charAt(0).toUpperCase()+t.slice(1)},r=(...e)=>e.filter((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();
/**
 * @license lucide-vue-next v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};
/**
 * @license lucide-vue-next v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s=({size:s,strokeWidth:i=2,absoluteStrokeWidth:a,color:c,iconNode:l,name:u,class:d,...f},{slots:h})=>e("svg",{...o,width:s||o.width,height:s||o.height,stroke:c||o.stroke,"stroke-width":a?24*Number(i)/Number(s):i,class:r("lucide",...u?[`lucide-${t(n(u))}-icon`,`lucide-${t(u)}`]:["lucide-icon"]),...f},[...l.map(t=>e(...t)),...h.default?[h.default()]:[]]),i=(t,n)=>(r,{slots:o})=>e(s,{...r,iconNode:n,name:t},o),a=i("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]),c=i("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),l=i("chart-no-axes-column-increasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]),u=i("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),d=i("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),f=i("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),h=i("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),p=i("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),y=i("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]),m=i("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),g=i("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),b=i("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),w=i("radio",[["path",{d:"M16.247 7.761a6 6 0 0 1 0 8.478",key:"1fwjs5"}],["path",{d:"M19.075 4.933a10 10 0 0 1 0 14.134",key:"ehdyv1"}],["path",{d:"M4.925 19.067a10 10 0 0 1 0-14.134",key:"1q22gi"}],["path",{d:"M7.753 16.239a6 6 0 0 1 0-8.478",key:"r2q7qm"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),E=i("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),O=i("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]),R=i("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),S=i("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),v=i("trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]),k=i("tv",[["path",{d:"m17 2-5 5-5-5",key:"16satq"}],["rect",{width:"20",height:"15",x:"2",y:"7",rx:"2",key:"1e6viu"}]]),x=i("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),A=i("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),T=i("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),j=i("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);
/**
 * @license lucide-vue-next v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */function C(e,t){return function(){return e.apply(t,arguments)}}const{toString:N}=Object.prototype,{getPrototypeOf:U}=Object,{iterator:P,toStringTag:L}=Symbol,_=(e=>t=>{const n=N.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),F=e=>(e=e.toLowerCase(),t=>_(t)===e),B=e=>t=>typeof t===e,{isArray:M}=Array,D=B("undefined");const q=F("ArrayBuffer");const z=B("string"),I=B("function"),H=B("number"),V=e=>null!==e&&"object"==typeof e,J=e=>{if("object"!==_(e))return!1;const t=U(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||L in e||P in e)},W=F("Date"),K=F("File"),$=F("Blob"),X=F("FileList"),G=F("URLSearchParams"),[Z,Q,Y,ee]=["ReadableStream","Request","Response","Headers"].map(F);function te(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),M(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let i;for(r=0;r<s;r++)i=o[r],t.call(null,e[i],i,e)}}function ne(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const re="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,oe=e=>!D(e)&&e!==re;const se=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&U(Uint8Array)),ie=F("HTMLFormElement"),ae=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),ce=F("RegExp"),le=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};te(n,(n,o)=>{let s;!1!==(s=t(n,o,e))&&(r[o]=s||n)}),Object.defineProperties(e,r)};const ue=F("AsyncFunction"),de=(fe="function"==typeof setImmediate,he=I(re.postMessage),fe?setImmediate:he?(pe=`axios@${Math.random()}`,ye=[],re.addEventListener("message",({source:e,data:t})=>{e===re&&t===pe&&ye.length&&ye.shift()()},!1),e=>{ye.push(e),re.postMessage(pe,"*")}):e=>setTimeout(e));var fe,he,pe,ye;const me="undefined"!=typeof queueMicrotask?queueMicrotask.bind(re):"undefined"!=typeof process&&process.nextTick||de,ge={isArray:M,isArrayBuffer:q,isBuffer:function(e){return null!==e&&!D(e)&&null!==e.constructor&&!D(e.constructor)&&I(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||I(e.append)&&("formdata"===(t=_(e))||"object"===t&&I(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&q(e.buffer),t},isString:z,isNumber:H,isBoolean:e=>!0===e||!1===e,isObject:V,isPlainObject:J,isReadableStream:Z,isRequest:Q,isResponse:Y,isHeaders:ee,isUndefined:D,isDate:W,isFile:K,isBlob:$,isRegExp:ce,isFunction:I,isStream:e=>V(e)&&I(e.pipe),isURLSearchParams:G,isTypedArray:se,isFileList:X,forEach:te,merge:function e(){const{caseless:t}=oe(this)&&this||{},n={},r=(r,o)=>{const s=t&&ne(n,o)||o;J(n[s])&&J(r)?n[s]=e(n[s],r):J(r)?n[s]=e({},r):M(r)?n[s]=r.slice():n[s]=r};for(let o=0,s=arguments.length;o<s;o++)arguments[o]&&te(arguments[o],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(te(t,(t,r)=>{n&&I(t)?e[r]=C(t,n):e[r]=t},{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,s,i;const a={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],r&&!r(i,e,t)||a[i]||(t[i]=e[i],a[i]=!0);e=!1!==n&&U(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:_,kindOfTest:F,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(M(e))return e;let t=e.length;if(!H(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[P]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:ie,hasOwnProperty:ae,hasOwnProp:ae,reduceDescriptors:le,freezeMethods:e=>{le(e,(t,n)=>{if(I(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];I(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return M(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:ne,global:re,isContextDefined:oe,isSpecCompliantForm:function(e){return!!(e&&I(e.append)&&"FormData"===e[L]&&e[P])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(V(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=M(e)?[]:{};return te(e,(e,t)=>{const s=n(e,r+1);!D(s)&&(o[t]=s)}),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:ue,isThenable:e=>e&&(V(e)||I(e))&&I(e.then)&&I(e.catch),setImmediate:de,asap:me,isIterable:e=>null!=e&&I(e[P])};function be(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}ge.inherits(be,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:ge.toJSONObject(this.config),code:this.code,status:this.status}}});const we=be.prototype,Ee={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ee[e]={value:e}}),Object.defineProperties(be,Ee),Object.defineProperty(we,"isAxiosError",{value:!0}),be.from=(e,t,n,r,o,s)=>{const i=Object.create(we);return ge.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),be.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};function Oe(e){return ge.isPlainObject(e)||ge.isArray(e)}function Re(e){return ge.endsWith(e,"[]")?e.slice(0,-2):e}function Se(e,t,n){return e?e.concat(t).map(function(e,t){return e=Re(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const ve=ge.toFlatObject(ge,{},null,function(e){return/^is[A-Z]/.test(e)});function ke(e,t,n){if(!ge.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=ge.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!ge.isUndefined(t[e])})).metaTokens,o=n.visitor||l,s=n.dots,i=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&ge.isSpecCompliantForm(t);if(!ge.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(ge.isDate(e))return e.toISOString();if(ge.isBoolean(e))return e.toString();if(!a&&ge.isBlob(e))throw new be("Blob is not supported. Use a Buffer instead.");return ge.isArrayBuffer(e)||ge.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,o){let a=e;if(e&&!o&&"object"==typeof e)if(ge.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(ge.isArray(e)&&function(e){return ge.isArray(e)&&!e.some(Oe)}(e)||(ge.isFileList(e)||ge.endsWith(n,"[]"))&&(a=ge.toArray(e)))return n=Re(n),a.forEach(function(e,r){!ge.isUndefined(e)&&null!==e&&t.append(!0===i?Se([n],r,s):null===i?n:n+"[]",c(e))}),!1;return!!Oe(e)||(t.append(Se(o,n,s),c(e)),!1)}const u=[],d=Object.assign(ve,{defaultVisitor:l,convertValue:c,isVisitable:Oe});if(!ge.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!ge.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),ge.forEach(n,function(n,s){!0===(!(ge.isUndefined(n)||null===n)&&o.call(t,n,ge.isString(s)?s.trim():s,r,d))&&e(n,r?r.concat(s):[s])}),u.pop()}}(e),t}function xe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function Ae(e,t){this._pairs=[],e&&ke(e,this,t)}const Te=Ae.prototype;function je(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ce(e,t,n){if(!t)return e;const r=n&&n.encode||je;ge.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(s=o?o(t,n):ge.isURLSearchParams(t)?t.toString():new Ae(t,n).toString(r),s){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}Te.append=function(e,t){this._pairs.push([e,t])},Te.toString=function(e){const t=e?function(t){return e.call(this,t,xe)}:xe;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class Ne{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){ge.forEach(this.handlers,function(t){null!==t&&e(t)})}}const Ue={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Pe={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Ae,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Le="undefined"!=typeof window&&"undefined"!=typeof document,_e="object"==typeof navigator&&navigator||void 0,Fe=Le&&(!_e||["ReactNative","NativeScript","NS"].indexOf(_e.product)<0),Be="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Me=Le&&window.location.href||"http://localhost",De={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Le,hasStandardBrowserEnv:Fe,hasStandardBrowserWebWorkerEnv:Be,navigator:_e,origin:Me},Symbol.toStringTag,{value:"Module"})),...Pe};function qe(e){function t(e,n,r,o){let s=e[o++];if("__proto__"===s)return!0;const i=Number.isFinite(+s),a=o>=e.length;if(s=!s&&ge.isArray(r)?r.length:s,a)return ge.hasOwnProp(r,s)?r[s]=[r[s],n]:r[s]=n,!i;r[s]&&ge.isObject(r[s])||(r[s]=[]);return t(e,n,r[s],o)&&ge.isArray(r[s])&&(r[s]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}(r[s])),!i}if(ge.isFormData(e)&&ge.isFunction(e.entries)){const n={};return ge.forEachEntry(e,(e,r)=>{t(function(e){return ge.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null}const ze={transitional:Ue,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=ge.isObject(e);o&&ge.isHTMLForm(e)&&(e=new FormData(e));if(ge.isFormData(e))return r?JSON.stringify(qe(e)):e;if(ge.isArrayBuffer(e)||ge.isBuffer(e)||ge.isStream(e)||ge.isFile(e)||ge.isBlob(e)||ge.isReadableStream(e))return e;if(ge.isArrayBufferView(e))return e.buffer;if(ge.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ke(e,new De.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return De.isNode&&ge.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((s=ge.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ke(s?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(ge.isString(e))try{return(t||JSON.parse)(e),ge.trim(e)}catch(r){if("SyntaxError"!==r.name)throw r}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ze.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(ge.isResponse(e)||ge.isReadableStream(e))return e;if(e&&ge.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(o){if(n){if("SyntaxError"===o.name)throw be.from(o,be.ERR_BAD_RESPONSE,this,null,this.response);throw o}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:De.classes.FormData,Blob:De.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};ge.forEach(["delete","get","head","post","put","patch"],e=>{ze.headers[e]={}});const Ie=ge.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),He=Symbol("internals");function Ve(e){return e&&String(e).trim().toLowerCase()}function Je(e){return!1===e||null==e?e:ge.isArray(e)?e.map(Je):String(e)}function We(e,t,n,r,o){return ge.isFunction(r)?r.call(this,t,n):(o&&(t=n),ge.isString(t)?ge.isString(r)?-1!==t.indexOf(r):ge.isRegExp(r)?r.test(t):void 0:void 0)}let Ke=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Ve(t);if(!o)throw new Error("header name must be a non-empty string");const s=ge.findKey(r,o);(!s||void 0===r[s]||!0===n||void 0===n&&!1!==r[s])&&(r[s||t]=Je(e))}const s=(e,t)=>ge.forEach(e,(e,n)=>o(e,n,t));if(ge.isPlainObject(e)||e instanceof this.constructor)s(e,t);else if(ge.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))s((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach(function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&Ie[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(ge.isObject(e)&&ge.isIterable(e)){let n,r,o={};for(const t of e){if(!ge.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?ge.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}s(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=Ve(e)){const n=ge.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(ge.isFunction(t))return t.call(this,e,n);if(ge.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ve(e)){const n=ge.findKey(this,e);return!(!n||void 0===this[n]||t&&!We(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Ve(e)){const o=ge.findKey(n,e);!o||t&&!We(0,n[o],o,t)||(delete n[o],r=!0)}}return ge.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!We(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return ge.forEach(this,(r,o)=>{const s=ge.findKey(n,o);if(s)return t[s]=Je(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(o):String(o).trim();i!==o&&delete t[o],t[i]=Je(r),n[i]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return ge.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&ge.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){const t=(this[He]=this[He]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Ve(e);t[r]||(!function(e,t){const n=ge.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})})}(n,e),t[r]=!0)}return ge.isArray(e)?e.forEach(r):r(e),this}};function $e(e,t){const n=this||ze,r=t||n,o=Ke.from(r.headers);let s=r.data;return ge.forEach(e,function(e){s=e.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function Xe(e){return!(!e||!e.__CANCEL__)}function Ge(e,t,n){be.call(this,null==e?"canceled":e,be.ERR_CANCELED,t,n),this.name="CanceledError"}function Ze(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new be("Request failed with status code "+n.status,[be.ERR_BAD_REQUEST,be.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}Ke.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),ge.reduceDescriptors(Ke.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),ge.freezeMethods(Ke),ge.inherits(Ge,be,{__CANCEL__:!0});const Qe=(e,t,n=3)=>{let r=0;const o=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,s=0,i=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[i];o||(o=c),n[s]=a,r[s]=c;let u=i,d=0;for(;u!==s;)d+=n[u++],u%=e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const f=l&&c-l;return f?Math.round(1e3*d/f):void 0}}(50,250);return function(e,t){let n,r,o=0,s=1e3/t;const i=(t,s=Date.now())=>{o=s,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-o;a>=s?i(e,t):(n=e,r||(r=setTimeout(()=>{r=null,i(n)},s-a)))},()=>n&&i(n)]}(n=>{const s=n.loaded,i=n.lengthComputable?n.total:void 0,a=s-r,c=o(a);r=s;e({loaded:s,total:i,progress:i?s/i:void 0,bytes:a,rate:c||void 0,estimated:c&&i&&s<=i?(i-s)/c:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},Ye=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},et=e=>(...t)=>ge.asap(()=>e(...t)),tt=De.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,De.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(De.origin),De.navigator&&/(msie|trident)/i.test(De.navigator.userAgent)):()=>!0,nt=De.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];ge.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),ge.isString(r)&&i.push("path="+r),ge.isString(o)&&i.push("domain="+o),!0===s&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function rt(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const ot=e=>e instanceof Ke?{...e}:e;function st(e,t){t=t||{};const n={};function r(e,t,n,r){return ge.isPlainObject(e)&&ge.isPlainObject(t)?ge.merge.call({caseless:r},e,t):ge.isPlainObject(t)?ge.merge({},t):ge.isArray(t)?t.slice():t}function o(e,t,n,o){return ge.isUndefined(t)?ge.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function s(e,t){if(!ge.isUndefined(t))return r(void 0,t)}function i(e,t){return ge.isUndefined(t)?ge.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,o,s){return s in t?r(n,o):s in e?r(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t,n)=>o(ot(e),ot(t),0,!0)};return ge.forEach(Object.keys(Object.assign({},e,t)),function(r){const s=c[r]||o,i=s(e[r],t[r],r);ge.isUndefined(i)&&s!==a||(n[r]=i)}),n}const it=e=>{const t=st({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:s,xsrfCookieName:i,headers:a,auth:c}=t;if(t.headers=a=Ke.from(a),t.url=Ce(rt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),ge.isFormData(r))if(De.hasStandardBrowserEnv||De.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(De.hasStandardBrowserEnv&&(o&&ge.isFunction(o)&&(o=o(t)),o||!1!==o&&tt(t.url))){const e=s&&i&&nt.read(i);e&&a.set(s,e)}return t},at="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=it(e);let o=r.data;const s=Ke.from(r.headers).normalize();let i,a,c,l,u,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=r;function p(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let y=new XMLHttpRequest;function m(){if(!y)return;const r=Ke.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders());Ze(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:r,config:e,request:y}),y=null}y.open(r.method.toUpperCase(),r.url,!0),y.timeout=r.timeout,"onloadend"in y?y.onloadend=m:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(m)},y.onabort=function(){y&&(n(new be("Request aborted",be.ECONNABORTED,e,y)),y=null)},y.onerror=function(){n(new be("Network Error",be.ERR_NETWORK,e,y)),y=null},y.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||Ue;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new be(t,o.clarifyTimeoutError?be.ETIMEDOUT:be.ECONNABORTED,e,y)),y=null},void 0===o&&s.setContentType(null),"setRequestHeader"in y&&ge.forEach(s.toJSON(),function(e,t){y.setRequestHeader(t,e)}),ge.isUndefined(r.withCredentials)||(y.withCredentials=!!r.withCredentials),d&&"json"!==d&&(y.responseType=r.responseType),h&&([c,u]=Qe(h,!0),y.addEventListener("progress",c)),f&&y.upload&&([a,l]=Qe(f),y.upload.addEventListener("progress",a),y.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(i=t=>{y&&(n(!t||t.type?new Ge(null,e,y):t),y.abort(),y=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===De.protocols.indexOf(g)?n(new be("Unsupported protocol "+g+":",be.ERR_BAD_REQUEST,e)):y.send(o||null)})},ct=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof be?t:new Ge(t instanceof Error?t.message:t))}};let s=t&&setTimeout(()=>{s=null,o(new be(`timeout ${t} of ms exceeded`,be.ETIMEDOUT))},t);const i=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)}),e=null)};e.forEach(e=>e.addEventListener("abort",o));const{signal:a}=r;return a.unsubscribe=()=>ge.asap(i),a}},lt=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},ut=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},dt=(e,t,n,r)=>{const o=async function*(e,t){for await(const n of ut(e))yield*lt(n,t)}(e,t);let s,i=0,a=e=>{s||(s=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return a(),void e.close();let s=r.byteLength;if(n){let e=i+=s;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},ft="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,ht=ft&&"function"==typeof ReadableStream,pt=ft&&("function"==typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),yt=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},mt=ht&&yt(()=>{let e=!1;const t=new Request(De.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),gt=ht&&yt(()=>ge.isReadableStream(new Response("").body)),bt={stream:gt&&(e=>e.body)};var wt;ft&&(wt=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!bt[e]&&(bt[e]=ge.isFunction(wt[e])?t=>t[e]():(t,n)=>{throw new be(`Response type '${e}' is not supported`,be.ERR_NOT_SUPPORT,n)})}));const Et=async(e,t)=>{const n=ge.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(ge.isBlob(e))return e.size;if(ge.isSpecCompliantForm(e)){const t=new Request(De.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return ge.isArrayBufferView(e)||ge.isArrayBuffer(e)?e.byteLength:(ge.isURLSearchParams(e)&&(e+=""),ge.isString(e)?(await pt(e)).byteLength:void 0)})(t):n},Ot={http:null,xhr:at,fetch:ft&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:f}=it(e);l=l?(l+"").toLowerCase():"text";let h,p=ct([o,s&&s.toAbortSignal()],i);const y=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let m;try{if(c&&mt&&"get"!==n&&"head"!==n&&0!==(m=await Et(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(ge.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=Ye(m,Qe(et(c)));r=dt(n.body,65536,e,t)}}ge.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;h=new Request(t,{...f,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:o?d:void 0});let s=await fetch(h,f);const i=gt&&("stream"===l||"response"===l);if(gt&&(a||i&&y)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});const t=ge.toFiniteNumber(s.headers.get("content-length")),[n,r]=a&&Ye(t,Qe(et(a),!0))||[];s=new Response(dt(s.body,65536,n,()=>{r&&r(),y&&y()}),e)}l=l||"text";let g=await bt[ge.findKey(bt,l)||"text"](s,e);return!i&&y&&y(),await new Promise((t,n)=>{Ze(t,n,{data:g,headers:Ke.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:h})})}catch(g){if(y&&y(),g&&"TypeError"===g.name&&/Load failed|fetch/i.test(g.message))throw Object.assign(new be("Network Error",be.ERR_NETWORK,e,h),{cause:g.cause||g});throw be.from(g,g&&g.code,e,h)}})};ge.forEach(Ot,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const Rt=e=>`- ${e}`,St=e=>ge.isFunction(e)||null===e||!1===e,vt=e=>{e=ge.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){let t;if(n=e[s],r=n,!St(n)&&(r=Ot[(t=String(n)).toLowerCase()],void 0===r))throw new be(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+s]=r}if(!r){const e=Object.entries(o).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new be("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Rt).join("\n"):" "+Rt(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function kt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ge(null,e)}function xt(e){kt(e),e.headers=Ke.from(e.headers),e.data=$e.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return vt(e.adapter||ze.adapter)(e).then(function(t){return kt(e),t.data=$e.call(e,e.transformResponse,t),t.headers=Ke.from(t.headers),t},function(t){return Xe(t)||(kt(e),t&&t.response&&(t.response.data=$e.call(e,e.transformResponse,t.response),t.response.headers=Ke.from(t.response.headers))),Promise.reject(t)})}const At="1.10.0",Tt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Tt[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const jt={};Tt.transitional=function(e,t,n){function r(e,t){return"[Axios v"+At+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,s)=>{if(!1===e)throw new be(r(o," has been removed"+(t?" in "+t:"")),be.ERR_DEPRECATED);return t&&!jt[o]&&(jt[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,s)}},Tt.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const Ct={assertOptions:function(e,t,n){if("object"!=typeof e)throw new be("options must be an object",be.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const t=e[s],n=void 0===t||i(t,s,e);if(!0!==n)throw new be("option "+s+" must be "+n,be.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new be("Unknown option "+s,be.ERR_BAD_OPTION)}},validators:Tt},Nt=Ct.validators;let Ut=class{constructor(e){this.defaults=e||{},this.interceptors={request:new Ne,response:new Ne}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(r){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=st(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&Ct.assertOptions(n,{silentJSONParsing:Nt.transitional(Nt.boolean),forcedJSONParsing:Nt.transitional(Nt.boolean),clarifyTimeoutError:Nt.transitional(Nt.boolean)},!1),null!=r&&(ge.isFunction(r)?t.paramsSerializer={serialize:r}:Ct.assertOptions(r,{encode:Nt.function,serialize:Nt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Ct.assertOptions(t,{baseUrl:Nt.spelling("baseURL"),withXsrfToken:Nt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&ge.merge(o.common,o[t.method]);o&&ge.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=Ke.concat(s,o);const i=[];let a=!0;this.interceptors.request.forEach(function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const c=[];let l;this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let u,d=0;if(!a){const e=[xt.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=i.length;let f=t;for(d=0;d<u;){const e=i[d++],t=i[d++];try{f=e(f)}catch(h){t.call(this,h);break}}try{l=xt.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return Ce(rt((e=st(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};ge.forEach(["delete","get","head","options"],function(e){Ut.prototype[e]=function(t,n){return this.request(st(n||{},{method:e,url:t,data:(n||{}).data}))}}),ge.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,o){return this.request(st(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Ut.prototype[e]=t(),Ut.prototype[e+"Form"]=t(!0)});const Pt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Pt).forEach(([e,t])=>{Pt[t]=e});const Lt=function e(t){const n=new Ut(t),r=C(Ut.prototype.request,n);return ge.extend(r,Ut.prototype,n,{allOwnKeys:!0}),ge.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(st(t,n))},r}(ze);Lt.Axios=Ut,Lt.CanceledError=Ge,Lt.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,o){n.reason||(n.reason=new Ge(e,r,o),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;return{token:new e(function(e){t=e}),cancel:t}}},Lt.isCancel=Xe,Lt.VERSION=At,Lt.toFormData=ke,Lt.AxiosError=be,Lt.Cancel=Lt.CanceledError,Lt.all=function(e){return Promise.all(e)},Lt.spread=function(e){return function(t){return e.apply(null,t)}},Lt.isAxiosError=function(e){return ge.isObject(e)&&!0===e.isAxiosError},Lt.mergeConfig=st,Lt.AxiosHeaders=Ke,Lt.formToJSON=e=>qe(ge.isHTMLForm(e)?new FormData(e):e),Lt.getAdapter=vt,Lt.HttpStatusCode=Pt,Lt.default=Lt;const{Axios:_t,AxiosError:Ft,CanceledError:Bt,isCancel:Mt,CancelToken:Dt,VERSION:qt,all:zt,Cancel:It,isAxiosError:Ht,spread:Vt,toFormData:Jt,AxiosHeaders:Wt,HttpStatusCode:Kt,formToJSON:$t,getAdapter:Xt,mergeConfig:Gt}=Lt;export{a as B,c as C,f as D,h as F,p as H,y as I,m as M,g as P,w as R,R as S,k as T,A as U,T as X,j as Y,x as a,Lt as b,O as c,E as d,u as e,d as f,l as g,b as h,S as i,v as j};
